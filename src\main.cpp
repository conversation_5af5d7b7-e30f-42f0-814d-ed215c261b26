#include <Arduino.h>
#include "Config.h"
#include "TDS_Sensor_UART.h"
#include "SensorDataManager.h"
#include "PowerManager.h"
#include "ConnectivityManager.h"
#include "ReportingLogic.h"

// Hardware configuration
HardwareSerial& SensorSerial = Serial1;
TDS_Sensor_UART tds_sensor(SensorSerial);

// System components
SensorDataManager dataManager;
PowerManager powerManager;
ConnectivityManager connectivity;
ReportingLogic reporting(dataManager, connectivity);

// Global state
bool systemInitialized = false;
unsigned long lastSensorRead = 0;

// Function declarations
void performSensorCycle();
SensorReading readSensorData();
void printSystemStatus(const SensorReading& reading);
void enterErrorSleep();
void printMemoryInfo();
void printSystemInfo();
bool handleWiFiConnection();
void enterPermanentDeepSleep();

void setup() {
  // Initialize serial communication
  Serial.begin(SERIAL_BAUDRATE);
  while (!Serial && millis() < 5000); // Wait up to 5 seconds for serial

  Serial.println("\n=== Fish Tank Sensor System ===");
  Serial.printf("Firmware Version: %s\n", FIRMWARE_VERSION);
  Serial.printf("Device: %s\n", DEVICE_NAME);

  // Initialize power manager first to check wake-up reason
  powerManager.begin();

  // Initialize sensor data manager
  dataManager.setPowerManager(&powerManager);
  if (!dataManager.begin()) {
    Serial.println("Failed to initialize data manager!");
    enterErrorSleep();
    return;
  }

  // Initialize sensor
  tds_sensor.begin(SENSOR_BAUDRATE, SERIAL_8N1, SENSOR_RX_PIN, SENSOR_TX_PIN);
  Serial.printf("Sensor initialized on RX=%d, TX=%d, Baud=%ld\n",
                SENSOR_RX_PIN, SENSOR_TX_PIN, SENSOR_BAUDRATE);

  // Initialize connectivity (BluFi mode)
  connectivity.begin(MQTT_SERVER, MQTT_PORT, MQTT_USER, MQTT_PASSWORD);

  // Initialize reporting logic
  reporting.begin();

  systemInitialized = true;

  Serial.printf("System initialized. Boot count: %u\n", dataManager.getBootCount());

  if (powerManager.isFirstBoot()) {
    Serial.println("First boot detected");
    printSystemInfo();

    // Handle WiFi connection for first boot
    if (!handleWiFiConnection()) {
      Serial.println("WiFi connection failed on first boot, entering permanent deep sleep");
      enterPermanentDeepSleep();
      return;
    }
  } else {
    Serial.println("Wake-up from deep sleep");

    // Handle WiFi connection for wake-up
    if (!handleWiFiConnection()) {
      Serial.println("WiFi connection failed after wake-up");
      if (connectivity.getWiFiFailureCount() >= 3) {
        Serial.println("Maximum WiFi failures reached, entering permanent deep sleep");
        enterPermanentDeepSleep();
        return;
      }
    }
  }

  if (DEBUG_ENABLED) {
    printMemoryInfo();
  }
}

void loop() {
  if (!systemInitialized) {
    enterErrorSleep();
    return;
  }

  // Main sensor reading and reporting cycle
  performSensorCycle();

  // Enter deep sleep until next reading
  Serial.println("Entering deep sleep...");
  powerManager.enterDeepSleep(SLEEP_MIN_SECONDS, SLEEP_MAX_SECONDS);
}

void performSensorCycle() {
  Serial.println("\n--- Starting Sensor Cycle ---");

  // Allow sensor to warm up
  while (millis() < SENSOR_WARMUP_DELAY_MS)
  {
    delay(0.1*SENSOR_WARMUP_DELAY_MS);
  }

  // Read sensor data
  SensorReading currentReading = readSensorData();

  if (!currentReading.valid) {
    Serial.println("Failed to read sensor data, will retry next cycle");
    return;
  }

  ReportDecision decision = reporting.analyzeReading(currentReading);
  dataManager.storeReading(currentReading);

  if (decision.shouldReport || DEBUG_ENABLED) {
    Serial.printf("Reporting needed: %s\n", decision.reasonText.c_str());

    // Execute the report
    bool reportSuccess = reporting.executeReport(currentReading, decision);

    if (reportSuccess) {
      Serial.println("Report sent successfully");
    } else {
      Serial.println("Report failed");
    }

    // Disconnect to save power
    connectivity.disconnect();
  } else {
    Serial.println("No reporting needed this cycle");
  }

  // Print current status
  printSystemStatus(currentReading);
}

SensorReading readSensorData() {
  SensorReading reading;

  Serial.println("Reading sensor data...");

  // Read TDS and temperature
  TDS_Sensor_UART::TDS_Data data = tds_sensor.read_tds_and_temp();

  if (data.valid) {
    reading.tds = data.tds;
    reading.temperature = data.temp;
    reading.timestamp = powerManager.getTotalUptime();  // Use total uptime including deep sleep
    reading.valid = true;

    Serial.printf("Sensor reading: TDS=%d ppm, Temp=%.2f°C, Timestamp=%lu ms\n",
                  reading.tds, reading.temperature, reading.timestamp);
  } else {
    Serial.println("Sensor reading failed");
    reading.valid = false;
  }

  return reading;
}

void printSystemStatus(const SensorReading& reading) {
  Serial.println("\n--- System Status ---");
  Serial.printf("Current reading: TDS=%d ppm, Temp=%.2f°C, Timestamp=%lu ms\n",
                reading.tds, reading.temperature, reading.timestamp);

  SensorReading lastReading = dataManager.getLastReading();
  if (lastReading.valid) {
    Serial.printf("Previous reading: TDS=%d ppm, Temp=%.2f°C, Timestamp=%lu ms\n",
                  lastReading.tds, lastReading.temperature, lastReading.timestamp);
  }

  Serial.printf("Free heap: %u bytes\n", ESP.getFreeHeap());
  Serial.printf("Current session uptime: %lu ms\n", millis());
  Serial.printf("Total uptime (including deep sleep): %llu ms\n", powerManager.getTotalUptime());

  reporting.printReportingStats();
  Serial.println("--------------------");
}

void enterErrorSleep() {
  Serial.println("Entering error sleep mode...");
  Serial.flush();

  // Sleep for minimum time on error
  powerManager.enterDeepSleep(SLEEP_MIN_SECONDS, SLEEP_MIN_SECONDS);
}

// Debug helper functions
void printMemoryInfo() {
  if (ENABLE_MEMORY_MONITORING) {
    Serial.printf("Free heap: %u bytes\n", ESP.getFreeHeap());
    Serial.printf("Largest free block: %u bytes\n", ESP.getMaxAllocHeap());
    Serial.printf("Min free heap: %u bytes\n", ESP.getMinFreeHeap());
  }
}

void printSystemInfo() {
  Serial.println("\n=== System Information ===");
  Serial.printf("Chip model: %s\n", ESP.getChipModel());
  Serial.printf("Chip revision: %d\n", ESP.getChipRevision());
  Serial.printf("CPU frequency: %d MHz\n", ESP.getCpuFreqMHz());
  Serial.printf("Flash size: %d bytes\n", ESP.getFlashChipSize());
  Serial.printf("SDK version: %s\n", ESP.getSdkVersion());
  printMemoryInfo();
  Serial.println("==========================");
}

bool handleWiFiConnection() {
  Serial.println("\n--- WiFi Connection Process ---");

  // Check if we have stored WiFi credentials
  if (!connectivity.hasStoredCredentials()) {
    Serial.println("No stored WiFi credentials found");

    // Start BluFi provisioning (3 minutes timeout)
    Serial.println("Starting BluFi provisioning...");
    if (!connectivity.startBluFiProvisioning(180000)) { // 3 minutes
      Serial.println("BluFi provisioning failed or timed out");
      return false;
    }

    Serial.println("BluFi provisioning completed successfully");
    return true;
  } else {
    Serial.println("Found stored WiFi credentials, attempting connection...");

    // Try to connect using stored credentials
    if (connectivity.connectWiFi(30000)) { // 30 seconds timeout
      Serial.println("WiFi connection successful");
      return true;
    } else {
      Serial.println("WiFi connection failed with stored credentials");
      return false;
    }
  }
}

void enterPermanentDeepSleep() {
  Serial.println("\n=== ENTERING PERMANENT DEEP SLEEP ===");
  Serial.println("Device will not wake up automatically");
  Serial.println("Reset the device to restart");
  Serial.flush();

  // Disconnect everything
  connectivity.disconnect();

  // Clear any wake-up sources
  esp_sleep_disable_wakeup_source(ESP_SLEEP_WAKEUP_ALL);

  // Enter deep sleep without any wake-up timer
  esp_deep_sleep_start();
}